import 'dart:math';

import 'package:decimal/decimal.dart';
import 'package:decimal/intl.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

import '../input_number.dart';
import 'input_number_keyboard_params.dart';

abstract class _KeyboardBehavior {
  void onTextInput(String text);

  void onBackspace();
}

const _kKeyHeight = 47.0;
const _kKeyMargin = 6.0;

final class GPNumbericKeyboard extends StatefulWidget {
  const GPNumbericKeyboard({
    required this.editingController,
    required this.params,
    super.key,
  });

  final TextEditingController editingController;

  final GPNumbericKeyboardParams params;

  @override
  State<GPNumbericKeyboard> createState() => _GIosPNumbericKeyboardState();
}

class _GIosPNumbericKeyboardState extends State<GPNumbericKeyboard>
    implements _KeyboardBehavior {
  late final DecimalFormatter format =
      DecimalFormatter(NumberFormat.simpleCurrency(
    locale: 'en_US',
    name: '',
    decimalDigits: widget.params.decimalDigits,
  ));

  final DecimalFormatter formatWithOutDecimals =
      DecimalFormatter(NumberFormat.simpleCurrency(
    locale: 'en_US',
    name: '',
    decimalDigits: 0,
  ));

  String _newNum = '0';
  String _newString = '';

  bool _isNegative = false;
  bool isDelete = false;
  bool isInsert = false;

  @override
  void onTextInput(String text) {
    isInsert = true;
    isDelete = false;

    if (text == widget.params.btnDoneStr) {
      widget.params.onDone(widget.editingController.text);
    } else {
      _insertText(text);
    }
  }

  @override
  void onBackspace() {
    isInsert = false;
    isDelete = true;

    final text = widget.editingController.text;
    final textSelection = widget.editingController.selection;
    final selectionLength = textSelection.end - textSelection.start;

    // There is a selection.
    if (selectionLength > 0) {
      final newText = text.replaceRange(
        textSelection.start,
        textSelection.end,
        '',
      );
      widget.editingController.text = newText;
      widget.editingController.selection = textSelection.copyWith(
        baseOffset: textSelection.start,
        extentOffset: textSelection.start,
      );
      return;
    }

    // The cursor is at the beginning.
    if (textSelection.start == 0) {
      return;
    }

    // Delete the previous character
    final previousCodeUnit = text.codeUnitAt(textSelection.start - 1);
    final offset = _isUtf16Surrogate(previousCodeUnit) ? 2 : 1;
    final newStart = textSelection.start - offset;
    final newEnd = textSelection.start;
    final newText = text.replaceRange(
      newStart,
      newEnd,
      '',
    );
    widget.editingController.text = newText;
    widget.editingController.selection = textSelection.copyWith(
      baseOffset: newStart,
      extentOffset: newStart,
    );

    _handleTextChange(text, newText, updateDecimal: false);
  }

  void _insertText(String myText) {
    isDelete = false;

    final oldText = widget.editingController.text;
    final textSelection = widget.editingController.selection;
    final newText = oldText.replaceRange(
      textSelection.start,
      textSelection.end,
      myText,
    );

    _handleTextChange(oldText, newText);
  }

  bool _isLessThanMinValue(String value) {
    if (widget.params.minValue == null || value.isEmpty) {
      return false;
    }

    return num.parse(value) < widget.params.minValue!;
  }

  bool _isMoreThanMaxValue(num value) {
    final maxDisplayIntCompare = value >= 999999999999999999;

    if (widget.params.maxValue == null) {
      return maxDisplayIntCompare;
    }

    final compare = value > widget.params.maxValue!;

    return compare || maxDisplayIntCompare;
  }

  bool _lastCharacterIsDigit(String text) {
    final String lastChar = text.substring(text.length - 1);
    return RegExp('[0-9]').hasMatch(lastChar);
  }

  String _parseStrToNum(String text) {
    if (text.isEmpty) {
      return text;
    }
    Decimal value = Decimal.tryParse(text) ?? Decimal.zero;
    // if (widget.params.decimalDigits > 0) {
    //   final result = (value /
    //           Decimal.parse(pow(10, widget.params.decimalDigits).toString()))
    //       .toDecimal();
    //   return result.toString();
    // }

    // case .0
    if (text.contains('.') && value.isInteger) {
      final afterDot = text.split('.').last;
      return "$value.$afterDot";
    }
    return value.toString();
  }

  void _formatter(
    String newText, {
    required bool isThousandSeparator,
    String? newValue,
    String? oldValue,
    int decimalDigits = 2,
  }) {
    _newNum = _parseStrToNum(newText);

    final bool endWithDot = newValue?.endsWith('.') ?? false;
    if (_newNum == '0' && _isNegative) {
      _newString = '-';
    } else {
      logDebug('newValue -> $newValue, $oldValue');
      if (isThousandSeparator) {
        // final isInputThousand =
        //     newValue?.replaceAll(oldValue ?? '', '') == '000';
        String formatedStr = _newNum.isEmpty
            ? _newNum
            : format.format(Decimal.parse(_newNum)).trim();
        String stringBeforeDot = formatedStr.contains('.')
            ? formatedStr.split('.').first
            : formatedStr;
        // if (isInputThousand) {
        //   stringBeforeDot =
        //       stringBeforeDot.substring(0, stringBeforeDot.length - 1);
        // }
        try {
          final firstStr =
              stringBeforeDot.isEmpty ? '' : stringBeforeDot.substring(0, 1);
          if (stringBeforeDot.startsWith('00')) {
            final afterDotStr = (formatedStr).split('.').last;

            formatedStr = afterDotStr.substring(
                afterDotStr.length - 1, afterDotStr.length);
          } else if (firstStr.startsWith('0') && firstStr != '0') {
            formatedStr = stringBeforeDot.substring(1, stringBeforeDot.length);
          } else {
            // ô input đang là 1. nhập thêm 1 số
            if (oldValue?.contains('.') == true) {
              String afterDotStr = newValue?.contains('.') == true
                  ? (newValue ?? '').split('.').last
                  : '';
              if (afterDotStr.length > decimalDigits) {
                afterDotStr = afterDotStr.substring(0, decimalDigits);
              }
              formatedStr = afterDotStr.isNotEmpty
                  ? '$stringBeforeDot.$afterDotStr'
                  : stringBeforeDot;
            } else {
              formatedStr = stringBeforeDot;
            }
          }
        } catch (ex) {
          logDebug('_formatter ex -> $ex');
        }

        _newString = formatedStr;
      } else {
        _newString = _newNum.toString().trim();
      }
    }

    // bỏ đi nếu xài `Tự động thêm n số 0 theo decimal digits`
    if (endWithDot && !_newString.endsWith('.')) {
      _newString += '.';
    }
  }

  void _handleTextChange(
    String oldValue,
    String newValue, {
    bool updateDecimal = true,
  }) {
    // if (newValue.endsWith('.') && isDelete) {
    //   newValue = newValue.substring(0, newValue.length - 1);
    //   isDelete = false;
    // }

    final params = widget.params;
    final bool isRemovedCharacter =
        oldValue.length - 1 == newValue.length && oldValue.startsWith(newValue);
    // final numberOfChangedCharacters =
    //     isRemovedCharacter ? 0 : newValue.length - oldValue.length;

    if (params.enableNegative) {
      _isNegative = newValue.startsWith('-');
    } else {
      _isNegative = false;
    }

    if (newValue.endsWith('-') && newValue.length > 2) {
      // đảo ngược âm / dương
      newValue = newValue.replaceAll('-', '');
      if (!_isNegative) {
        newValue = '-$newValue';
      }

      _isNegative = !_isNegative;
    }

    final newValueIndexOfNegative = newValue.indexOf('-');
    final isNewValueIndexOfNegative = newValueIndexOfNegative == 1;
    if (newValue == '-' || newValue == '--' || isNewValueIndexOfNegative) {
      if (isNewValueIndexOfNegative) {
        widget.editingController.text = '-';
        widget.editingController.selection =
            const TextSelection.collapsed(offset: 1);
      } else {
        widget.editingController.text = _isNegative ? '-' : '';
        widget.editingController.selection =
            TextSelection.collapsed(offset: _isNegative ? 1 : 0);
      }

      return;
    }

    final newValueIndexOfDots = newValue.indexOf('.');

    String checkDecimalDigits() {
      // kiểm tra phần thập phân, nếu quá số lượng cho phép,
      // => tịnh tiến dấu chấm lên số lượng ký tự tương ứng.
      int newValueLength =
          newValueIndexOfDots == -1 ? newValue.length : newValueIndexOfDots;

      final newValueSplits = newValue.split('.');
      final newValueHasDot =
          newValueSplits.length > 1 ? newValueSplits.last : '';
      // final needUpdateDecimal =
      //     (newValueIndexOfDots + params.decimalDigits + 1) >
      //         newValueSplits.length;
      String newText = '';

      // user nhập phần thập phân không quá số lượng số thập phân cho phép
      if (newValueHasDot.length <= params.decimalDigits) {
        return newValue.replaceAll(',', '');
      }

      newText = newValue
          .substring(0, newValueLength)
          .replaceAll(RegExp('[^0-9]'), '');

      // if (newValueHasDot.isNotEmpty &&
      //     needUpdateDecimal &&
      //     updateDecimal &&
      //     !newValue.endsWith('-') &&
      //     !skipCheckDecimalDigits) {
      //   newText = newValue
      //       .replaceAll('.', '')
      //       .substring(0, newValueLength + numberOfChangedCharacters)
      //       .replaceAll(RegExp('[^0-9]'), '');
      // } else {
      //   newText = newValue
      //       .substring(0, newValueLength)
      //       .replaceAll(RegExp('[^0-9]'), '');
      // }

      if (newValueSplits.length > 1) {
        return '$newText.${newValueSplits.last.substring(0, params.decimalDigits)}';
      }

      return newText;
    }

    String newText = checkDecimalDigits();

    if (isRemovedCharacter && !_lastCharacterIsDigit(oldValue)) {
      final int length =
          oldValue.endsWith('.') ? newText.length : newText.length - 1;
      newText = newText.substring(0, length > 0 ? length : 0);
    }

    final String value = _parseStrToNum(newText);

    if (_isLessThanMinValue(value)) {
      if (updateDecimal) {
        // if (_isMoreThanMaxValue(value)) {
        //   // nhập giá trị lớn hơn maxValue
        //   widget.editingController.text = oldValue;
        //   widget.editingController.selection =
        //       TextSelection.collapsed(offset: oldValue.length);
        //   params.onMaxValueVerify?.call(newValue, oldValue);
        //   return;
        // }
      } else {
        params.onMinValueVerify?.call(newValue, oldValue);
      }
    }

    _formatter(
      newText,
      isThousandSeparator: params.isThousandSeparator,
      newValue: newValue,
      oldValue: oldValue,
      decimalDigits: params.decimalDigits,
    );

    if (newText.trim() == '' || newText == '00' || newText == '000') {
      widget.editingController.text = _isNegative ? '-' : '';
      widget.editingController.selection =
          TextSelection.collapsed(offset: _isNegative ? 1 : 0);
    }

    widget.editingController.text = _newString;

    if (newValueIndexOfDots > 0) {
      // String digitStr =
      //     '.${newValue.substring(newValueIndexOfDots, newValue.length).replaceAll('.', '').replaceAll('-', '')}';
      // if (digitStr.length > params.decimalDigits + 1) {
      //   digitStr =
      //       '.${digitStr.replaceAll('.', '').substring(1, digitStr.length - 1)}';
      // }

      /*
        Tự động thêm n số 0 theo decimal digits
      */
      // if (digitStr == '.' && updateDecimal) {
      //   String digitNumber = '';
      //   for (var i = 0; i < params.decimalDigits; i++) {
      //     digitNumber += '0';
      //   }

      //   if (newText.isEmpty) {
      //     widget.editingController.text += '0$digitStr$digitNumber';
      //   } else {
      //     widget.editingController.text += '$digitStr$digitNumber';
      //   }
      // } else {
      //   // vd params.decimalDigits = 2
      //   // 0.00 bấm thêm 000, hiển thị 0.00
      //   if ((digitStr.length - 1) > params.decimalDigits) {
      //     // digitStr = digitStr.substring(0, params.decimalDigits + 1);

      //     final String newValueStr =
      //         newValue.replaceAll(',', '').replaceAll('.', '');
      //     final start = newValueStr.length - params.decimalDigits;
      //     newText =
      //         '${newValueStr.substring(0, start)}.${newValueStr.substring(start, newValueStr.length)}';
      //     // _formatter(
      //     //   newText,
      //     //   isThousandSeparator: params.isThousandSeparator,
      //     //   newValue: newValue,
      //     //   oldValue: oldValue,
      //     // );

      //     _handleTextChange(
      //       newValue,
      //       newText,
      //       updateDecimal: updateDecimal,
      //       skipCheckDecimalDigits: true,
      //     );
      //     return;
      //   }
      //   widget.editingController.text += digitStr;
      // }
    }

    String formatedCurrentText = widget.editingController.text;
    if (!isDelete) {
      if (formatedCurrentText.contains('.') && isInsert) {
        final splitted = formatedCurrentText.split('.');
        // nếu endWith .
        // replace đi để tránh kết quả .0
        if (splitted.last.length >= widget.params.decimalDigits) {
          String input = formatedCurrentText;
          if (formatedCurrentText.endsWith('.')) {
            input = input.replaceAll('.', '');
          }
          formatedCurrentText = input;
        } else {
          if (formatedCurrentText.contains('.')) {
            final splited = formatedCurrentText.split('.');
            final beforeDotStr = splited.first;
            final afterDotStr = splited.last;
            formatedCurrentText = '${beforeDotStr}.$afterDotStr';
          }
        }
      }

      widget.editingController.text = formatedCurrentText;
    } else {
      final text = widget.editingController.text;
      if (text.contains('.')) {
        final splitted = text.split('.');
        final beforeDotStr = splitted.first;
        final afterDotStr = splitted.last;

        final newNum = beforeDotStr;
        widget.editingController.text = '${newNum}.$afterDotStr';
      } else {
        widget.editingController.text = text.isEmpty ? '' : text;
      }
    }

    widget.editingController.selection = TextSelection.collapsed(
      offset: widget.editingController.text.length,
    );
  }

  bool _isUtf16Surrogate(int value) {
    return value & 0xF800 == 0xD800;
  }

  double width(BuildContext context, {int numberOfItems = 4}) {
    return MediaQuery.of(context).size.width / numberOfItems - _kKeyMargin / 2;
  }

  double btnDoneHeight() {
    if (widget.params.decimalDigits == 0) {
      return _kKeyHeight * 3 + _kKeyMargin * 3;
    }

    return _kKeyHeight * 2 + _kKeyMargin * 2;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: GPNumbericSoftKeyboard.kKeyboardHeight,
      padding: const EdgeInsets.only(bottom: 20),
      color: const Color(0xFFD1D5DB),
      child: SafeArea(
        top: false,
        bottom: true,
        child: Column(
          children: [
            Row(children: [
              Expanded(
                child: _KeyWidget(
                  text: '1',
                  behavior: this,
                ),
              ),
              Expanded(
                child: _KeyWidget(
                  text: '2',
                  behavior: this,
                ),
              ),
              Expanded(
                child: _KeyWidget(
                  text: '3',
                  behavior: this,
                ),
              ),
              Expanded(
                child: _KeyWidget(
                  text: '-',
                  behavior: this,
                ),
              ),
              const SizedBox(width: _kKeyMargin),
            ]),

            Stack(
              alignment: AlignmentDirectional.bottomEnd,
              children: [
                Column(
                  children: [
                    Row(children: [
                      Expanded(
                        child: _KeyWidget(
                          text: '4',
                          behavior: this,
                        ),
                      ),
                      Expanded(
                        child: _KeyWidget(
                          text: '5',
                          behavior: this,
                        ),
                      ),
                      Expanded(
                        child: _KeyWidget(
                          text: '6',
                          behavior: this,
                        ),
                      ),
                      Expanded(
                        child: _KeyWidget(
                          text: '.',
                          behavior: this,
                        ),
                      ),
                      const SizedBox(width: _kKeyMargin),
                    ]),
                    Row(children: [
                      Expanded(
                        child: _KeyWidget(
                          text: '7',
                          behavior: this,
                        ),
                      ),
                      Expanded(
                        child: _KeyWidget(
                          text: '8',
                          behavior: this,
                        ),
                      ),
                      Expanded(
                        child: _KeyWidget(
                          text: '9',
                          behavior: this,
                        ),
                      ),
                      const Expanded(
                        child: SizedBox(),
                      ),
                      const SizedBox(width: _kKeyMargin)
                    ]),
                    Row(children: [
                      if (widget.params.isThousandSeparator)
                        Expanded(
                          child: _KeyWidget(
                            text: '000',
                            behavior: this,
                          ),
                        ),
                      Expanded(
                        flex: widget.params.isThousandSeparator ? 1 : 2,
                        child: _KeyWidget(
                          text: '0',
                          behavior: this,
                        ),
                      ),
                      Expanded(
                        child: _BackKeyWidget(
                          behavior: this,
                        ),
                      ),
                      const Expanded(
                        child: SizedBox(),
                      ),
                      const SizedBox(width: _kKeyMargin)
                    ]),
                  ],
                ),
                Align(
                  alignment: AlignmentDirectional.bottomEnd,
                  child: SizedBox(
                    width: width(context),
                    height: btnDoneHeight(),
                    child: _KeyWidget(
                      text: widget.params.btnDoneStr,
                      bgColor:
                          widget.params.btnDoneColor ?? const Color(0xff007AFF),
                      textColor: Colors.white,
                      fontSize: 16,
                      behavior: this,
                    ),
                  ),
                ).paddingOnly(right: _kKeyMargin)
              ],
            ),

            ///
          ],
        ),
      ),
    );
  }
}

class _KeyWidget extends StatelessWidget {
  const _KeyWidget({
    required this.text,
    required this.behavior,
    this.bgColor,
    this.textColor,
    this.fontSize,
  });

  final String text;
  final Color? bgColor, textColor;
  final double? fontSize;

  final _KeyboardBehavior behavior;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => behavior.onTextInput(text),
      borderRadius: BorderRadius.circular(6),
      child: _Container(
        bgColor: bgColor ?? Colors.white,
        child: Text(
          text,
          style: TextStyle(
            fontSize: fontSize ?? 25,
            color: textColor ?? Colors.black,
          ),
        ),
      ),
    );
  }
}

class _BackKeyWidget extends StatelessWidget {
  const _BackKeyWidget({
    required this.behavior,
  });

  final _KeyboardBehavior behavior;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: behavior.onBackspace,
      child: const _Container(
        child: SvgWidget(Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_SYMBOL_SVG),
      ),
    );
  }
}

class _Container extends StatelessWidget {
  const _Container({
    required this.child,
    this.bgColor = Colors.white,
  });

  final Color bgColor;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: _kKeyHeight,
      margin: const EdgeInsets.fromLTRB(6, 6, 0, 0),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(6),
        border: const Border(
          bottom: BorderSide(width: 1, color: Color(0xff898A8D)),
        ),
      ),
      child: Center(child: child),
    );
  }
}
