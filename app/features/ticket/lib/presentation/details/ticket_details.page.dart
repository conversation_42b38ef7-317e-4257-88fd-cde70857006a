import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';

import '../../domain/entity/entity.dart';
import 'details.dart';

class TicketDetailsPage extends StatefulWidget {
  const TicketDetailsPage({
    required this.id,
    this.entity,
    super.key,
  });

  final String id;
  final TicketEntity? entity;

  @override
  State<TicketDetailsPage> createState() => _TicketDetailsPageState();
}

class _TicketDetailsPageState extends State<TicketDetailsPage>
    with TickerProviderStateMixin, TicketReloadDataMixin {
  late final TicketDetailsBloc detailsBloc = TicketDetailsBloc();

  late TicketEntity? ticketEntity = widget.entity;

  late TicketDetailsData detailsData = TicketDetailsData(ticketId: widget.id);

  final tabs = TicketDetailsTabsEnum.values.toList();

  final ValueNotifier<bool> rxIsLoading = ValueNotifier(false);

  late TabController tabController;

  void showLoading() {
    rxIsLoading.value = true;
  }

  void hideLoading() {
    rxIsLoading.value = false;
  }

  @override
  void initState() {
    super.initState();

    showLoading();

    // SLA chưa làm trong phase 1
    // tabs.remove(TicketDetailsTabsEnum.sla);

    initTabController();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  void initTabController() {
    tabController = TabController(length: tabs.length, vsync: this);
  }

  void _hideKeyboard(BuildContext context) {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  void _onBack(BuildContext context) {
    final result = handleReloadDataWhenBack(
      ticketEntity: detailsData.ticketEntity,
      ticketNodeEntity: detailsData.nodeEntity,
      ticketReloadDataActionEnum: detailsData.ticketReloadDataActionEnum,
    );

    Navigator.pop(context, result);
  }

  void handleTicketState(BuildContext context, TicketDetailsState state) {
    if (state is TicketDetailsLoadingState) {
      showLoading();
    } else if (state is TicketDetailsLoadingDismissedState) {
      hideLoading();
    } else if (state is TicketDetailsInitialTabState) {
      detailsData = detailsData.copyWith(
        ticketEntity: state.ticketEntity,
        flowChartEntity: state.flowChartEntity,
        nodeEntity: state.nodeEntity,
        ticketActionEntity: state.ticketActionEntity,
      );
      if (state.ticketActionEntity != null &&
          state.ticketActionEntity?.actions.contains(TicketAction.comment) !=
              true) {
        tabs.remove(TicketDetailsTabsEnum.comments);
        initTabController();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TicketDetailsBloc>(
      create: (context) => detailsBloc,
      child: GestureDetector(
        onTap: () => _hideKeyboard(context),
        behavior: HitTestBehavior.opaque,
        child: Stack(
          children: [
            Scaffold(
              backgroundColor: GPColor.bgSecondary,
              appBar: AppBar(
                leading: BackButton(
                  onPressed: () => _onBack(context),
                ),
                elevation: 0,
                title: Column(
                  children: [
                    Text(
                      LocaleKeys.ticket_details_appbar_title.tr,
                      style: textStyle(GPTypography.headingMedium),
                    ),
                  ],
                ),
              ),
              body: BlocConsumer<TicketDetailsBloc, TicketDetailsState>(
                listener: (context, state) {
                  handleTicketState(context, state);
                },
                buildWhen: (previous, current) {
                  return current is TicketDetailsInitialTabState;
                },
                builder: (context, state) {
                  // if (ticketEntity == null) return const SizedBox();

                  return Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: Ink(
                          color: GPColor.bgPrimary,
                          child: TabBar(
                            controller: tabController,
                            tabs: tabs
                                .map(
                                  (e) => Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12.0),
                                    child: Tab(text: e.displayName),
                                  ),
                                )
                                .toList(),
                            isScrollable: true,
                            labelColor: GPColor.functionAccentWorkSecondary,
                            padding: EdgeInsets.zero,
                            labelPadding: EdgeInsets.zero,
                            labelStyle: textStyle(GPTypography.headingMedium),
                            unselectedLabelStyle:
                                textStyle(GPTypography.headingMedium),
                            unselectedLabelColor: GPColor.contentSecondary,
                            indicatorColor: GPColor.functionAccentWorkSecondary,
                            indicatorPadding:
                                const EdgeInsets.symmetric(horizontal: 8),
                            onTap: (index) => _hideKeyboard(context),
                          ),
                        ),
                      ),
                      Divider(
                        color: GPColor.lineTertiary,
                        height: 1,
                      ),
                      Expanded(
                        child: TabBarView(
                          controller: tabController,
                          children: tabs
                              .map(
                                (e) => e.toWidget(
                                  detailsData: detailsData,
                                  ticketDetailsBloc: detailsBloc,
                                  onUpdateData: (ticketEntity,
                                      ticketAdditionalRequestEntities) {
                                    // update widget
                                    detailsData.ticketEntity = ticketEntity;
                                    detailsData
                                            .ticketAdditionalRequestEntities =
                                        ticketAdditionalRequestEntities;
                                  },
                                ),
                              )
                              .toList(),
                        ),
                      ),
                      Divider(
                        color: GPColor.lineTertiary,
                        height: 1,
                      ),
                    ],
                  );
                },
              ),
            ),
            ValueListenableBuilder(
              valueListenable: rxIsLoading,
              builder: (context, value, child) {
                if (!value) return const SizedBox();

                return GestureDetector(
                  onTap: () {
                    // doNothing, no need to dismiss dialog here
                  },
                  child: Container(
                    color: Colors.transparent,
                    width: double.infinity,
                    height: double.infinity,
                    child: Center(
                      child: Container(
                        height: 60,
                        width: 60,
                        decoration: BoxDecoration(
                            color: const Color(0x50000000),
                            borderRadius: BorderRadius.circular(16)),
                        child: CupertinoActivityIndicator(
                          color: GPColor.bgPrimary,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// hành động cuối cùng của user tương tác trên Ticket
enum TicketReloadDataActionEnum {
  create,
  duplicate,
  addHandler,
  requestToProvideInfo,
  onHold,
  onHoldNeedApprove,
  approveOnHold,
  rejectOnHold,
  continueHandle,
  cancel,
  unfollow,
  delete,
  approve,
  reject,
  close,
  reopen,
  addLabels,
  addFollowers,
  nextStep,
  spam,
  editField,
  backStep,
  backEndStep,
}

mixin TicketReloadDataMixin {
  ///
  /// Dựa vào `ticketEntity`
  /// cập nhật lại dữ liệu màn hình trước đó (list)
  /// khi user back
  ///
  /// Tạo yêu cầu: nav to Tạo bởi tôi > tất cả </br>
  /// Tạo bản sao yêu cầu: nav to Tạo bởi tôi > tất cả </br>
  /// Thêm người thực hiện: nav to Tạo bởi tôi > tất cả </br>
  /// Yêu cầu bổ sung: nav to Gửi đến tôi > Chờ bổ sung </br>
  /// Bảo lưu yêu cầu: nav to Gửi đến tôi > Chờ duyệt bảo lưu </br>
  /// Duyệt bảo lưu: nav to Tạo bởi tôi > Bảo lưu </br>
  /// Không duyệt bảo lưu: nav to Gửi đến tôi > Đang xử lý </br>
  /// Chuyển bước sau: nav to Gửi đến tôi > Đã xử lý </br>
  /// Hủy: nav to Tạo bởi tôi > Đã hủy </br>
  ///
  List<dynamic> handleReloadDataWhenBack({
    TicketEntity? ticketEntity,
    TicketNodeEntity? ticketNodeEntity,
    TicketReloadDataActionEnum? ticketReloadDataActionEnum,
  }) {
    if (ticketReloadDataActionEnum == null) return [];

    /* 
      có thể cần xử lý logic ở `ticketEntity`, `ticketNodeEntity` sau này
      hiện chỉ bắt theo action đơn => có case sẽ bị navigate sai tab
    */

    if (ticketReloadDataActionEnum == TicketReloadDataActionEnum.create ||
            ticketReloadDataActionEnum == TicketReloadDataActionEnum.duplicate
        // || ticketReloadDataActionEnum == TicketReloadDataActionEnum.reopen
        ) {
      return [
        TicketHomeMenuEnums.createdByMe,
        TicketHomeTabs.all,
      ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.requestToProvideInfo) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.waitingForProvideInfo,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.onHold) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.archived,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.onHoldNeedApprove) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.waitingForArchive,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.approveOnHold) {
      //   return [
      //     TicketHomeMenuEnums.archiveSendToMe,
      //     TicketHomeTabs.approved,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.rejectOnHold) {
      //   return [
      //     TicketHomeMenuEnums.archiveSendToMe,
      //     TicketHomeTabs.notApprove,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.nextStep) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.handled,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.continueHandle) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.handling,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.cancel) {
      //   return [
      //     null,
      //     TicketHomeTabs.cancelled,
      //   ];
      // } else if (ticketReloadDataActionEnum == TicketReloadDataActionEnum.spam) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.spam,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.approve) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.approved,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //     TicketReloadDataActionEnum.reject) {
      //   return [
      //     TicketHomeMenuEnums.sendToMe,
      //     TicketHomeTabs.notApprove,
      //   ];
      // } else if (ticketReloadDataActionEnum == TicketReloadDataActionEnum.close) {
      //   return [
      //     TicketHomeMenuEnums.createdByMe,
      //     TicketHomeTabs.closed,
      //   ];
      // } else if (ticketReloadDataActionEnum ==
      //         TicketReloadDataActionEnum.backStep ||
      //     ticketReloadDataActionEnum == TicketReloadDataActionEnum.backEndStep) {
      //   return [
      //     null,
      //     TicketHomeTabs.handling,
      //   ];
    }

    return [null, null];
  }
}
