import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

/// Constants for AI Meeting Info Screen
class _AIMeetingInfoConstants {
  static const double horizontalPadding = 24.0;
  static const double verticalSpacing = 24.0;
  static const double sectionSpacing = 16.0;
  static const double itemSpacing = 12.0;
  static const double borderRadius = 8.0;
  static const double contactBorderRadius = 12.0;
  static const double contactPadding = 16.0;
  static const double buttonPadding = 16.0;
  static const double buttonVerticalPadding = 8.0;
  static const double titleLineHeight = 1.33;
  static const double contactHeaderSpacing = 4.0;
  static const double contactItemSpacing = 16.0;
  static const double contactTitleFontSize = 12.0;

  // Contact information
  static const String hotlineNumber = '1900.633.388';
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '0247.3000.585';

  // Shadow configuration
  static const Color shadowColor = Color(0x1A2F313A);
  static const double shadowBlurRadius = 1.0;
  static const Offset shadowOffset = Offset(0, -1);
}

class AIMeetingInfoScreen extends StatelessWidget {
  const AIMeetingInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GPColor.bgPrimary,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: _AIMeetingInfoConstants.horizontalPadding,
                ),
                child: Column(
                  children: [
                    const SizedBox(
                        height: _AIMeetingInfoConstants.verticalSpacing),
                    _buildBannerImage(),
                    const SizedBox(
                        height: _AIMeetingInfoConstants.verticalSpacing),
                    _buildTitle(),
                    const SizedBox(
                        height: _AIMeetingInfoConstants.sectionSpacing),
                    _buildFeatureList(),
                    const SizedBox(
                        height: _AIMeetingInfoConstants.sectionSpacing),
                    _buildContactSection(),
                    const SizedBox(
                        height: _AIMeetingInfoConstants.verticalSpacing),
                  ],
                ),
              ),
            ),
            _buildBottomButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildBannerImage() {
    return SizedBox(
      width: double.infinity,
      height: 148,
      child: ClipRRect(
        borderRadius: const BorderRadius.all(
          Radius.circular(_AIMeetingInfoConstants.borderRadius),
        ),
        child: Image.asset(
          Assets.PACKAGES_GP_ASSETS_IMAGES_AI_MEETING_BANNER_PNG,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      LocaleKeys.calendar_ai_meeting_info_page_title.tr,
      textAlign: TextAlign.center,
      style: textStyle(GPTypography.displaySmall)?.copyWith(
        color: GPColor.contentPrimary,
        height: _AIMeetingInfoConstants.titleLineHeight,
      ),
    );
  }

  Widget _buildFeatureList() {
    return Column(
      children: [
        _FeatureItem(
          emoji: '👉',
          boldText:
              LocaleKeys.calendar_ai_meeting_info_page_description_bold_1.tr,
          text: LocaleKeys.calendar_ai_meeting_info_page_description_1.tr,
        ),
        const SizedBox(height: _AIMeetingInfoConstants.itemSpacing),
        _FeatureItem(
          emoji: '👉',
          boldText:
              LocaleKeys.calendar_ai_meeting_info_page_description_bold_2.tr,
          text: LocaleKeys.calendar_ai_meeting_info_page_description_2.tr,
        ),
      ],
    );
  }

  Widget _buildContactSection() {
    return _ContactSection();
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: _AIMeetingInfoConstants.buttonPadding,
        vertical: _AIMeetingInfoConstants.buttonVerticalPadding,
      ),
      decoration: BoxDecoration(
        color: GPColor.bgPrimary,
        boxShadow: const [
          BoxShadow(
            color: _AIMeetingInfoConstants.shadowColor,
            blurRadius: _AIMeetingInfoConstants.shadowBlurRadius,
            offset: _AIMeetingInfoConstants.shadowOffset,
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: SizedBox(
          width: double.infinity,
          child: GPWorkButton(
            title: LocaleKeys.calendar_ai_meeting_info_page_understand.tr,
            onTap: Utils.back,
          ),
        ),
      ),
    );
  }
}

/// Feature item widget displaying emoji, bold text, and description
class _FeatureItem extends StatelessWidget {
  const _FeatureItem({
    required this.emoji,
    required this.boldText,
    required this.text,
  });

  final String emoji;
  final String boldText;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          emoji,
          style: textStyle(GPTypography.bodyMedium),
        ),
        const SizedBox(width: _AIMeetingInfoConstants.itemSpacing),
        Expanded(
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: boldText,
                  style: textStyle(GPTypography.headingMedium)?.copyWith(
                    color: GPColor.contentPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextSpan(
                  text: text,
                  style: textStyle(GPTypography.bodyLarge)?.copyWith(
                    color: GPColor.contentSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// Contact section widget containing hotline and contact information
class _ContactSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(_AIMeetingInfoConstants.contactPadding),
      decoration: BoxDecoration(
        border: Border.all(color: GPColor.linePrimary),
        borderRadius: BorderRadius.circular(
          _AIMeetingInfoConstants.contactBorderRadius,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildContactHeader(),
          const SizedBox(height: _AIMeetingInfoConstants.contactItemSpacing),
          _buildContactDetails(),
        ],
      ),
    );
  }

  Widget _buildContactHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.calendar_ai_meeting_info_page_contact_title.tr,
          style: textStyle(GPTypography.headingSmall)?.copyWith(
            color: GPColor.contentSecondary,
            fontSize: _AIMeetingInfoConstants.contactTitleFontSize,
          ),
        ),
        const SizedBox(height: _AIMeetingInfoConstants.contactHeaderSpacing),
        InkWell(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          onTap: () => Deeplink.onTapUrl(_AIMeetingInfoConstants.hotlineNumber),
          child: Text(
            _AIMeetingInfoConstants.hotlineNumber,
            style: textStyle(GPTypography.displayLarge)?.copyWith(
              color: GPColor.blue,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactDetails() {
    return Column(
      children: [
        _ContactItem(
          icon: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_MAIL_SVG,
          text: _AIMeetingInfoConstants.supportEmail,
        ),
        const SizedBox(height: _AIMeetingInfoConstants.itemSpacing),
        _ContactItem(
          icon: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PHONE_SVG,
          text: _AIMeetingInfoConstants.supportPhone,
        ),
      ],
    );
  }
}

/// Contact item widget for displaying contact information with icon
class _ContactItem extends StatelessWidget {
  const _ContactItem({
    required this.icon,
    required this.text,
  });

  final String icon;
  final String text;

  static const double _iconSize = 20.0;
  static const double _iconSpacing = 8.0;
  static const double _textLineHeight = 1.2;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () => Deeplink.onTapUrl(text),
      child: Row(
        children: [
          SizedBox(
            width: _iconSize,
            height: _iconSize,
            child: SvgWidget(
              icon,
              width: _iconSize,
              height: _iconSize,
              color: GPColor.functionAccentWorkSecondary,
            ),
          ),
          const SizedBox(width: _iconSpacing),
          Expanded(
            child: Text(
              text,
              style: textStyle(GPTypography.bodyLarge)?.copyWith(
                color: GPColor.contentPrimary,
                height: _textLineHeight,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
