import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/extensions/date_time_extension.dart';
import 'package:gp_core/widgets/gradient_box_border.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_calendar/screens/create/create_calendar_event_controller.dart';

class AI<PERSON>eetingBox extends StatelessWidget {
  const AIMeetingBox({super.key, required this.controller});

  final CreateCalendarEventController controller;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: GradientBoxBorder(
              gradient: const LinearGradient(
            colors: [Color(0xFF4BAFF6), Color(0xFFFBA446)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          )),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      SizedBox(
                        width: 40,
                        height: 40,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: Image.asset(Assets
                              .PACKAGES_GP_ASSETS_IMAGES_AI_BOT_ASSISTANT_AVATAR_PNG),
                        ),
                      ),
                      Positioned(
                        right: -4,
                        bottom: -4,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: GPColor.blue,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Bot',
                            style:
                                textStyle(GPTypography.headingSmall)?.copyWith(
                              color: GPColor.blue,
                              height: 1.33,
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                      child: Row(
                    children: [
                      Text(
                        LocaleKeys.calendar_ai_meeting_calendar_box_title.tr,
                        style: textStyle(GPTypography.headingSmall)
                            ?.copyWith(height: 1.2),
                      ),
                      InkWell(
                        onTap: () => _showAIAssistantInfoScreen(),
                        child: Padding(
                          padding: const EdgeInsets.all(4),
                          child: SvgWidget(
                            Assets
                                .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_LINE15_INFORMATIONMARK_CIRCLE_SVG,
                            color: GPColor.contentSecondary,
                          ),
                        ),
                      )
                    ],
                  )),
                  const SizedBox(width: 12),
                  Obx(() {
                    final isRepeating = controller.isRepeatingEvent;
                    final switchWidget = SizedBox(
                      width: 40,
                      height: 26,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: CupertinoSwitch(
                          value: controller.hasAIMeeting.value,
                          onChanged: isRepeating
                              ? null
                              : controller.onAIAssistantChanged,
                          activeTrackColor: GPColor.functionAccentWorkSecondary,
                          inactiveThumbColor: controller.isRepeatingEvent
                              ? GPColor.contentSecondary
                              : null,
                        ),
                      ),
                    );

                    if (isRepeating) {
                      return InfoTooltipWidget(
                        controller: controller.aiAsistantTooltipController,
                        tooltipText: LocaleKeys
                            .calendar_ai_meeting_calendar_box_tooltip.tr,
                        direction: TooltipDirection.up,
                        arrowTipDistance: 20,
                        textAlign: TextAlign.left,
                        child: switchWidget,
                      );
                    }

                    return switchWidget;
                  }),
                ],
              ),
              const SizedBox(height: 18),
              Text(
                LocaleKeys.calendar_ai_meeting_calendar_box_main_function.tr,
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(color: GPColor.contentSecondary),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    LocaleKeys
                        .calendar_ai_meeting_calendar_box_remaining_count.tr,
                    style: textStyle(GPTypography.bodyMedium)
                        ?.copyWith(color: GPColor.contentSecondary),
                  ),
                  const SizedBox(width: 4),
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                          text:
                              controller.aiMeetingRemainingRequests.toString(),
                          style: textStyle(GPTypography.headingSmall)?.copyWith(
                              color: controller.aiMeetingRemainingRequests == 0
                                  ? GPColor.functionNegativePrimary
                                  : GPColor.functionAccentWorkSecondary)),
                      TextSpan(
                          text: "/${controller.aiMeetingBotTotalRequest}",
                          style: textStyle(GPTypography.bodyMedium)
                              ?.copyWith(color: GPColor.contentSecondary)),
                    ]),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                "${LocaleKeys.calendar_ai_meeting_calendar_box_reset_time.tr} ${DateTimeUtils.instance.getNextMonday().formatddMMYY}",
                style: textStyle(GPTypography.bodyMedium)
                    ?.copyWith(color: GPColor.contentSecondary),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAIAssistantInfoScreen() {
    Get.toNamed(CalendarRouterName.aiAssistantInfo);
  }
}
