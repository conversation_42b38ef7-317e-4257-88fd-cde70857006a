import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';

import 'app_behavior/assignee_picker_behavior.dart';
import 'app_behavior/calendar_behavior.dart';
import 'app_behavior/general_behavior.dart';
import 'app_behavior/rtf_editor_behavior.dart';
import 'app_behavior/task_behavior.dart';
import 'app_behavior/ticket_behavior.dart';

/*
updated 07/06/2024
Flow:
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
|                         ios                         |     |                                    flutter                                    |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
| 1. makeEngine: khởi tạo engine                      | <-> | Init app, call method "flutterInit" sau khi init xong (updated at 07/06/2024) |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
| 2. setAppInfo: truyền token cho phía Flutter        | ->  | Nhận callback kèm params: receive token info                                  |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
| 3. sendMoreInfo: mở màn hình tương ứng phía Flutter | ->  | Nhận entryPoint, kèm params: open entryPoint tương ứng                        |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
*/

// const _maxRetry = 1;

typedef AfterSetAppInfoCallBack = Future<bool> Function();

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen>
    with
        GapoAppGeneralBehavior,
        GapoAppTaskBehavior,
        GapoAppCalendarBehavior,
        GapoAppRtfEditorBehavior,
        GapoAppAssigneePickerBehavior,
        GapoAppTicketBehavior,
        _DebugMixin {
  final MethodChannel _channel = Deeplink.channel;
  String routeName = "";
  String nextRoute = "";

  Timer? timer;

  /// retry time for all callback
  int currentRetry = 0;

  void appendMessage(String message, {Map<String, dynamic>? data}) {
    if (Platform.isIOS) {
      GPCoreTracker().appendMessage(message, data: data);
    }
  }

  void _setupChannel() {
    nextRoute = Get.parameters["next"] ?? "";
    routeName = nextRoute;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _channel.setMethodCallHandler((call) async {
        logDebug(
            'receive global method call\nmethod: ${call.method}\nargs: ${call.arguments}');

        appendMessage(
          'receive global method call',
          data: {
            'method': call.method,
            'args': call.arguments.toString(),
            'route': {
              'current_route': Get.currentRoute,
              'next_route': routeName,
            }
          },
        );

        _onMethodCallHandler(call, routeName);
      });
    });
  }

  Future<bool> afterSetAppInfo(
    AfterSetAppInfoCallBack afterSetAppInfoFnc,
  ) {
    if (isSetAppInfo) {
      return afterSetAppInfoFnc();
    } else {
      // add to queue, handle after setAppInfo
      queues.add(afterSetAppInfoFnc);

      return Future.value(true);
    }
  }

  Future _onMethodCallHandler(MethodCall call, String currentRoute) async {
    bool result = false;
    try {
      appendDebugMessage(
          '* callMethod: ${call.method} \ncurrentRoute:$currentRoute - ${Get.currentRoute}');

      GPCoreTracker().appendError(
        'Flutter.app._onMethodCallHandler',
        data: {
          'message':
              '* callMethod: ${call.method} \ncurrentRoute:$currentRoute - ${Get.currentRoute}'
        },
      );

      if (call.method == DeepLinkMethod.setLanguage) {
        result = await afterSetAppInfo(() {
          return onSetLanguage(call);
        });
      } else if (call.method == DeepLinkMethod.updateTokenInfo) {
        result = await afterSetAppInfo(
          () => onUpdateTokenInfo(call, currentRoute),
        );
      } else if (call.method == DeepLinkMethod.updateUserInfo) {
        result = await afterSetAppInfo(
          () => onUpdateUserInfo(call),
        );
      } else if (call.method == "setAppInfo") {
        result = await onSetAppInfo(call, currentRoute);
      } else if (call.method == "setAccessToken") {
        result = await afterSetAppInfo(
          () => onSetAccessToken(call, currentRoute),
        );
      } else if (call.method == "setTaskArguments") {
        result = await afterSetAppInfo(
          () => onSetTaskArguments(call),
        );
      } else if (call.method == "setTicketArguments") {
        result = await afterSetAppInfo(() => onSetTicketArguments(call));
      } else if (call.method == "setProject") {
        result = await afterSetAppInfo(
          () => onSetProject(call, currentRoute),
        );
      } else if (call.method == "setProjectArguments") {
        result = await afterSetAppInfo(
          () => onSetProjectArguments(call, currentRoute),
        );
      } else if (call.method == DeepLinkMethod.setEvent) {
        result = await afterSetAppInfo(
          () => onSetEvent(call),
        );
      } else if (call.method == "setAlbum") {
        return _handleError('not implemented ${call.method}');
      } else if (call.method == "pop") {
        result = await onPop(call);
      } else if (call.method == "refreshEventList") {
        if (!isSetAppInfo) return false;

        result = await onRefreshEventList(call);
      } else if (call.method == DeepLinkMethod.refreshOutlookSyncIfNeeded) {
        result = await afterSetAppInfo(
          () => onRefreshOutlookSyncIfNeeded(call),
        );
      } else if (call.method == DeepLinkMethod.logout) {
        result = await afterSetAppInfo(
          () => onLogout(call),
        );
      } else if (call.method == DeepLinkMethod.initializeCalendarCollab) {
        result = await afterSetAppInfo(
          () => onInitializeCalendarCollab(call),
        );
      } else if (call.method == DeepLinkMethod.scrollToTop) {
        result = await onScrollToTop(call);
      } else if (call.method == DeepLinkMethod.setPlatformNavigatorParams) {
        result = await onSetPlatformNavigatorParams(call);
      } else if (call.method == DeepLinkMethod.syncSuccessfully) {
        result = await afterSetAppInfo(
          () => onSyncSuccessfullys(call),
        );
      } else if (call.method == DeepLinkMethod.initializeTaskCollab) {
        result = await afterSetAppInfo(
          () => onInitializeTaskCollab(call, currentRoute),
        );
      } else if (call.method == DeepLinkMethod.refreshTaskCollab) {
        result = await afterSetAppInfo(
          () => onRefreshTaskCollab(call),
        );
      } else if (call.method == DeepLinkMethod.initTaskListData) {
        result = await afterSetAppInfo(
          () => onInitTaskListData(call),
        );
      } else if (call.method == DeepLinkMethod.initializeAssigneePicker) {
        result = await afterSetAppInfo(
          () => onInitializeAssigneePicker(call),
        );
      } else if (call.method == DeepLinkMethod.initializeRtfEditor) {
        result = await afterSetAppInfo(
          () => onInitializeRtfEditor(call),
        );
      } else if (call.method == DeepLinkMethod.updateSwipeBackGestureIfNeeded) {
        result = await onUpdateSwipeBackGestureIfNeeded(call);
      } else if (call.method == DeepLinkMethod.createTaskFromMessage) {
        result = await afterSetAppInfo(
          () => onCreateTaskFromMessage(call),
        );
      } else if (call.method == DeepLinkMethod.aiMeetingBotInfo) {
        result = await afterSetAppInfo(
          () => onOpenAIMeetingBotInfo(call),
        );
      } else {
        // `throw exception` native sẽ không nhận được callback
        return _handleError('not implemented ${call.method}');
      }
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.app._setupChannel',
        data: {'ex': e, 'stackTrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.app._setupChannel',
      );
      appendDebugMessage('* has error: $e, \n$s');

      // `throw exception` native sẽ không nhận được callback
      Get.offAndToNamed(RouterName.emptyScreen);
      return _handleError("Got an error ${e.toString()} from Flutter");
    }

    appendDebugMessage('========= ${call.method}: $result');
    logDebug('========= ${call.method}: $result');

    if (result == false) {
      GPCoreTracker().sendLog(
        message: 'Flutter.app._setupChannel: result = false',
      );
    }
  }

  /// Không sử dụng `throw exception` vì native sẽ không nhận được callback
  Map<String, dynamic> _handleError(
    String message, {
    String? code,
  }) {
    logDebug(
        "DEBUG: _handleError methodChannel with code: $code, message: $message");

    SentryEvent(
      message: SentryMessage('Loading Screen $message - $code'),
      logger: "Flutter:main",
      level: SentryLevel.error,
      fingerprint: [message],
    );

    return {
      "code": code ?? "",
      "message": message,
    };
  }

  @override
  void onAppendError(String error) {
    appendDebugMessage('* onAppendError -> $error');
  }

  @override
  void initState() {
    super.initState();

    _setupChannel();

    if (GetPlatform.isIOS) {
      // delay 1 khoảng thời gian để native có thể nhận được callback
      // nếu không delay sẽ xảy ra tình trạng trắng màn hình ở trên 1 vài thiết bị ios 15 trở lên.
      final delayMs = (routeName == CalendarRouterName.calendar ||
              routeName ==
                  RouterName.routeWithoutAnimation(CalendarRouterName.calendar))
          ? 150
          : 500;

      Future.delayed(Duration(milliseconds: delayMs)).then((v) {
        Deeplink.flutterInit();
      });
    }
  }

  Future _init() {
    return Future.delayed(const Duration(seconds: 2));
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _init(),
      builder: (context, snapshot) {
        return Scaffold(
          appBar: AppBar(
            leading: snapshot.hasData ? GPBackButton() : const SizedBox(),
            elevation: 0,
          ),
          body: Center(
            child: CircularProgressIndicator.adaptive(),
          ),
        );
      },
    );
  }
}

mixin _DebugMixin {
  File? logFile;
  Future<File?> initLogFile() async {
    // if (kDebugTool == false) return null;

    final String logPath =
        await Utils.getDownloadFileName('debug_gapo_flutter.txt');

    File file = File(logPath);

    if (!file.existsSync()) {
      file.createSync(recursive: true);
    }

    logFile = file;
    appendDebugMessage('----------\n${DateTime.now().toString()}');

    return logFile;
  }

  Future appendDebugMessage(String message) async {
    // 17/06: doNothing now
    return;

    // if (kDebugTool == false) return null;
    if (logFile == null) return null;

    await logFile?.writeAsString('$message \n\n', mode: FileMode.append);
  }
}
