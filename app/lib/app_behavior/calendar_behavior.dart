/*
 * Created Date: Sunday, 20th April 2025, 13:59:31
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 20th April 2025 14:16:42
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'dart:collection';

import 'package:flutter/services.dart';
import 'package:gp_core/base/networking/base/feature_flag.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_calendar/screens/collab/collab_calendar_controller.dart';
import 'package:gp_feat_calendar/screens/detail/models/calendar_event_detail_input.dart';
import 'package:gp_feat_calendar/screens/detail/models/flutter_calendar_detail_input.dart';
import 'package:gp_feat_calendar/screens/list/controller.dart';
import 'package:gp_feat_calendar/screens/list/models/calendar_collab_input.dart';

import 'app_behavior.dart';

mixin Gapo<PERSON>pp<PERSON>alendarBehavior implements GapoAppChannelBehavior {
  @override
  Future<bool> onSetEvent(MethodCall call) async {
    final arguments = call.arguments;
    // Code iOS đã refactor qua dùng object và có deeplink url mỗi khi mở màn detail
    // Android vẫn đang tự parse data từ deeplink và pass từng field qua
    // TODO: Refactor android
    if (GetPlatform.isIOS) {
      try {
        final args = CalendarEventDetailInput.fromDynamicInputCalledFromNative(
            arguments);
        Get.offAndToNamed(
          RouterName.routeWithoutAnimation(
              CalendarRouterName.calendarEventDetail),
          arguments: args,
        );
      } catch (e, trace) {
        GPCoreTracker().appendError(
          'Flutter.calendar.ios.handleSetEventMethod',
          data: {'ex': e, 'stackTrace': trace},
        );
        GPCoreTracker().sendLog(
          message: 'Flutter.app._setupChannel',
        );
        logDebug(e);
        logDebug(trace);

        onAppendError(e.toString());
        return false;
      }
      return true;
    }

    String id = '';
    int? startAt, endAt;

    CalendarEventDetailInput? calendarEventDetailInput;

    // native chỉ pass sang id
    if (arguments is String) {
      id = arguments;
    } else if (arguments is Map) {
      // existing logic
      // id
      if (arguments.containsKey('id') || arguments.containsKey('eventId')) {
        if (arguments['id'] != null) {
          id = arguments['id'];
        }
        if (id.isEmpty && arguments['eventId'] != null) {
          id = arguments['eventId'];
        }
      }

      // collab,
      // arguments['url'] có dạng:
      // https://staging7.gapowork.vn/collab/6998660684329531392/meet/6759670d8710ec7090f5848e?identity=2024_11_11
      if (id.isEmpty && arguments['url'] != null) {
        Uri uri = Uri.parse(arguments['url']);
        id = uri.pathSegments.last;
      }

      // start at
      var startAtRaw = arguments['start_at'];
      if (startAtRaw is String) {
        startAt = int.tryParse(startAtRaw);
      } else if (startAtRaw is int) {
        startAt = startAtRaw;
      }

      // end at
      var endAtRaw = arguments['end_at'];
      if (endAtRaw is String) {
        endAt = int.tryParse(endAtRaw);
      } else if (endAtRaw is int) {
        endAt = endAtRaw;
      }

      // refactored logic
      // thử parse ra thành object, nếu được thì dùng còn k thì keep logic cũ
      try {
        final deeplink = FlutterCalendarDetailInput.fromJson(
                Map<String, dynamic>.from(arguments))
            .deeplink;
        if (deeplink?.isNotEmpty == true) {
          final i = FlutterCalendarDetailInput.fromDeeplink(deeplink!);
          if (i != null) {
            calendarEventDetailInput =
                CalendarEventDetailInput.fromFlutterCalendarDetailInput(
              i,
              scenario: CalendarDetailScenario.native_,
            );
          }
        }
      } catch (e, trace) {
        GPCoreTracker().appendError(
          'Flutter.calendar.deeplink.handleSetEventMethod',
          data: {'ex': e, 'stackTrace': trace},
        );
        GPCoreTracker().sendLog(
          message: 'Flutter.calendar.deeplink.handleSetEventMethod',
        );
        logDebug(e);
        logDebug(trace);

        onAppendError(e.toString());

        return false;
      }
    }

    Get.offAndToNamed(
      RouterName.routeWithoutAnimation(CalendarRouterName.calendarEventDetail),
      arguments: calendarEventDetailInput ??
          CalendarEventDetailInput(
            scenario: CalendarDetailScenario.native_,
            eventId: id,
            startAt: startAt,
            endAt: endAt,
          ),
    );

    return true;
  }

  @override
  Future<bool> onRefreshEventList(MethodCall call) async {
    bool? forceData;
    Map<String, dynamic> arguments = {};

    if (call.arguments is HashMap || call.arguments is Map) {
      arguments = HashMap.from(call.arguments);
      forceData = arguments["forceData"];
    }

    if (Get.isRegistered<CalendarListController>()) {
      if (GetPlatform.isAndroid) {
        Get.find<CalendarListController>().refreshData(forceData: forceData);
      } else {
        Get.find<CalendarListController>()
            .refreshData(forceData: forceData ?? false);
      }

      return true;
    } else if (Get.isRegistered<CollabCalendarListController>()) {
      Get.find<CollabCalendarListController>()
          .refreshData(forceData: forceData);
      return true;
    } else {
      logDebug(
          "CalendarListController and CollabCalendarListController are not found!");
      return false;
    }
  }

  @override
  Future<bool> onRefreshOutlookSyncIfNeeded(MethodCall call) async {
    if (Get.isRegistered<CalendarListController>()) {
      if (FeatureFlag.enableSyncOutlook) {
        Get.find<CalendarListController>().checkMSSync();
        return true;
      }
    }

    logDebug("CalendarListController is not found!");
    return false;
  }

  @override
  Future<bool> onLogout(MethodCall call) async {
    if (Get.isRegistered<CalendarListController>()) {
      if (FeatureFlag.enableSyncOutlook) {
        /// logout outlook session và xóa local user
        Get.find<CalendarListController>().removeOutlookLocalUser();
        return true;
      }
    }

    logDebug("CalendarListController is not found!");
    return false;
  }

  @override
  Future<bool> onInitializeCalendarCollab(MethodCall call) async {
    try {
      final input = CalendarCollabInput.fromNativeArguments(call.arguments);
      Get.offAndToNamed(
          RouterName.routeWithoutAnimation(CalendarRouterName.calendarCollab),
          arguments: input);

      return true;
    } catch (e, trace) {
      GPCoreTracker().appendError(
        'Flutter.calendar._handleInitializeCalendarCollabEvent',
        data: {'ex': e, 'stackTrace': trace},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar._handleInitializeCalendarCollabEvent',
      );
      logDebug(e);
      logDebug(trace);
      Popup.instance.showSnackBar(
        type: SnackbarType.error,
        message: e.toString(),
      );

      onAppendError(e.toString());
    }

    return false;
  }

  @override
  Future<bool> onSyncSuccessfullys(MethodCall call) async {
    if (Get.isRegistered<CalendarListController>()) {
      Get.find<CalendarListController>().getNotifySyncSuccessfully();
      return true;
    }
    logDebug("CalendarListController is not found!");
    return false;
  }

  @override
  Future<bool> onOpenAIMeetingBotInfo(MethodCall call) async {
    try {
      Get.offAndToNamed(
        RouterName.routeWithoutAnimation(CalendarRouterName.aiAssistantInfo),
      );
      return true;
    } catch (ex) {
      rethrow;
    }
  }
}
